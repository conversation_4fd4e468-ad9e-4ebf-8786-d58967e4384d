import { useState } from "react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useRouter } from "@tanstack/react-router"
import { GoogleLoginButton } from "@/components/auth/google-login-button"
import { GithubLoginButton } from "@/components/auth/github-login-button"

export default function SignupForm() {
  const [errorMessage, setErrorMessage] = useState("")
  const router = useRouter()

  // Handler for auth success - redirect to login page after signup
  const handleAuthSuccess = () => {
    router.navigate({ to: '/login' })
  }

  // Handler for auth errors
  const handleAuthError = (error: Error) => {
    setErrorMessage(`Sign up failed: ${error.message || "Unknown error"}`)
  }

  return (
    <div className="space-y-4">
      {errorMessage && (
        <Alert variant="destructive">
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-3">
        <GoogleLoginButton
          onSuccess={handleAuthSuccess}
          onError={handleAuthError}
          mode="signup"
        />

        <GithubLoginButton
          onSuccess={handleAuthSuccess}
          onError={handleAuthError}
          mode="signup"
        />
      </div>
    </div>
  )
}
